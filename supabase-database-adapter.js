/**
 * Supabase Database Adapter for StreamOnPod
 * 
 * This adapter replaces the SQLite database connection with Supabase client
 * and provides compatibility methods for existing code.
 */

const { createClient } = require('@supabase/supabase-js');

class SupabaseDatabaseAdapter {
  constructor() {
    this.supabase = null;
    this.isConnected = false;
    this.init();
  }

  init() {
    try {
      const supabaseUrl = process.env.SUPABASE_URL;
      const supabaseKey = process.env.SUPABASE_SERVICE_KEY; // Use service key for server-side operations
      
      if (!supabaseUrl || !supabaseKey) {
        throw new Error('Supabase credentials not found in environment variables');
      }

      this.supabase = createClient(supabaseUrl, supabaseKey, {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      });

      this.isConnected = true;
      console.log('✅ Supabase database adapter initialized');
    } catch (error) {
      console.error('❌ Supabase initialization failed:', error.message);
      throw error;
    }
  }

  // Compatibility method for SQLite-style queries
  async run(sql, params = []) {
    try {
      // This is a simplified adapter - you'll need to convert SQL to Supabase operations
      console.warn('⚠️  Direct SQL execution not recommended with Supabase. Use specific methods instead.');
      
      // For now, return a mock result
      return {
        changes: 1,
        lastID: Date.now()
      };
    } catch (error) {
      throw new Error(`Query execution failed: ${error.message}`);
    }
  }

  // Compatibility method for SQLite-style single row queries
  async get(sql, params = []) {
    try {
      console.warn('⚠️  Direct SQL execution not recommended with Supabase. Use specific methods instead.');
      return null;
    } catch (error) {
      throw new Error(`Query execution failed: ${error.message}`);
    }
  }

  // Compatibility method for SQLite-style multi-row queries
  async all(sql, params = []) {
    try {
      console.warn('⚠️  Direct SQL execution not recommended with Supabase. Use specific methods instead.');
      return [];
    } catch (error) {
      throw new Error(`Query execution failed: ${error.message}`);
    }
  }

  // User operations
  async createUser(userData) {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .insert([userData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`User creation failed: ${error.message}`);
    }
  }

  async getUserById(userId) {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
      return data;
    } catch (error) {
      throw new Error(`User retrieval failed: ${error.message}`);
    }
  }

  async getUserByUsername(username) {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('*')
        .eq('username', username)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      throw new Error(`User retrieval failed: ${error.message}`);
    }
  }

  async getUserByEmail(email) {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('*')
        .eq('email', email)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      throw new Error(`User retrieval failed: ${error.message}`);
    }
  }

  async updateUser(userId, updates) {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`User update failed: ${error.message}`);
    }
  }

  // Stream operations
  async createStream(streamData) {
    try {
      const { data, error } = await this.supabase
        .from('streams')
        .insert([streamData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`Stream creation failed: ${error.message}`);
    }
  }

  async getStreamById(streamId) {
    try {
      const { data, error } = await this.supabase
        .from('streams')
        .select('*')
        .eq('id', streamId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      throw new Error(`Stream retrieval failed: ${error.message}`);
    }
  }

  async getStreamsByUserId(userId) {
    try {
      const { data, error } = await this.supabase
        .from('streams')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new Error(`Streams retrieval failed: ${error.message}`);
    }
  }

  async updateStream(streamId, updates) {
    try {
      const { data, error } = await this.supabase
        .from('streams')
        .update(updates)
        .eq('id', streamId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`Stream update failed: ${error.message}`);
    }
  }

  async deleteStream(streamId) {
    try {
      const { error } = await this.supabase
        .from('streams')
        .delete()
        .eq('id', streamId);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      throw new Error(`Stream deletion failed: ${error.message}`);
    }
  }

  // Video operations
  async createVideo(videoData) {
    try {
      const { data, error } = await this.supabase
        .from('videos')
        .insert([videoData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`Video creation failed: ${error.message}`);
    }
  }

  async getVideoById(videoId) {
    try {
      const { data, error } = await this.supabase
        .from('videos')
        .select('*')
        .eq('id', videoId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      throw new Error(`Video retrieval failed: ${error.message}`);
    }
  }

  async getVideosByUserId(userId) {
    try {
      const { data, error } = await this.supabase
        .from('videos')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new Error(`Videos retrieval failed: ${error.message}`);
    }
  }

  // Subscription operations
  async getUserSubscription(userId) {
    try {
      const { data, error } = await this.supabase
        .from('user_subscriptions')
        .select(`
          *,
          subscription_plans (*)
        `)
        .eq('user_id', userId)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      throw new Error(`Subscription retrieval failed: ${error.message}`);
    }
  }

  async createSubscription(subscriptionData) {
    try {
      const { data, error } = await this.supabase
        .from('user_subscriptions')
        .insert([subscriptionData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`Subscription creation failed: ${error.message}`);
    }
  }

  async getSubscriptionPlans() {
    try {
      const { data, error } = await this.supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('price', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new Error(`Subscription plans retrieval failed: ${error.message}`);
    }
  }

  // Transaction operations
  async createTransaction(transactionData) {
    try {
      const { data, error } = await this.supabase
        .from('transactions')
        .insert([transactionData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`Transaction creation failed: ${error.message}`);
    }
  }

  async getTransactionsByUserId(userId) {
    try {
      const { data, error } = await this.supabase
        .from('transactions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new Error(`Transactions retrieval failed: ${error.message}`);
    }
  }

  // Notification operations
  async createNotification(notificationData) {
    try {
      const { data, error } = await this.supabase
        .from('notifications')
        .insert([notificationData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(`Notification creation failed: ${error.message}`);
    }
  }

  async getNotificationsByUserId(userId, limit = 50) {
    try {
      const { data, error } = await this.supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      throw new Error(`Notifications retrieval failed: ${error.message}`);
    }
  }

  // Utility methods
  async executeTransaction(operations) {
    try {
      // Supabase doesn't have explicit transactions like SQLite
      // You would need to implement compensation logic or use database functions
      console.warn('⚠️  Transactions not directly supported. Consider using database functions.');
      
      for (const operation of operations) {
        await operation();
      }
      
      return { success: true };
    } catch (error) {
      throw new Error(`Transaction failed: ${error.message}`);
    }
  }

  async close() {
    // Supabase client doesn't need explicit closing
    this.isConnected = false;
    console.log('✅ Supabase connection closed');
  }

  // Health check
  async healthCheck() {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('count')
        .limit(1);

      if (error) throw error;
      return { healthy: true, connected: this.isConnected };
    } catch (error) {
      return { healthy: false, error: error.message };
    }
  }
}

// Export singleton instance
const supabaseDb = new SupabaseDatabaseAdapter();
module.exports = supabaseDb;
