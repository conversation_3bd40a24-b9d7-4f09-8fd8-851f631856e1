#!/usr/bin/env node

/**
 * StreamOnPod SQLite to Supabase Migration Executor
 * 
 * This tool provides:
 * 1. Automated Supabase project setup
 * 2. Schema migration from SQLite to PostgreSQL
 * 3. Data export and import with integrity verification
 * 4. Migration progress tracking and rollback capabilities
 */

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const { execSync } = require('child_process');

class SupabaseMigrationExecutor {
  constructor() {
    this.sqliteDbPath = './db/streamonpod.db';
    this.migrationBackupPath = `./db/streamonpod_pre_supabase_${Date.now()}.db`;
    this.migrationLogPath = './supabase_migration_log.txt';
    this.schemaOutputPath = './supabase_schema.sql';
    this.dataOutputPath = './supabase_data.sql';
    this.log = [];
    this.migrationSteps = [
      'backup_creation',
      'supabase_setup',
      'schema_analysis',
      'schema_migration',
      'data_export',
      'data_import',
      'code_refactoring',
      'testing',
      'verification'
    ];
    this.currentStep = 0;
  }

  async executeMigration() {
    console.log('🚀 StreamOnPod SQLite to Supabase Migration\n');
    
    try {
      // Step 1: Create comprehensive backup
      await this.createMigrationBackup();
      
      // Step 2: Analyze current schema and data
      await this.analyzeCurrentDatabase();
      
      // Step 3: Generate Supabase schema
      await this.generateSupabaseSchema();
      
      // Step 4: Export data for migration
      await this.exportDataForMigration();
      
      // Step 5: Generate migration instructions
      await this.generateMigrationInstructions();
      
      this.logMessage('✅ Migration preparation completed successfully!');
      this.logMessage('📋 Next steps: Follow the generated migration instructions');
      
      // Save migration log
      fs.writeFileSync(this.migrationLogPath, this.log.join('\n'));
      console.log(`\n📋 Migration log saved to: ${this.migrationLogPath}`);
      
    } catch (error) {
      this.logMessage(`❌ Migration failed: ${error.message}`);
      console.error('Migration failed:', error);
    }
  }

  logMessage(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    this.log.push(logEntry);
    console.log(message);
  }

  async createMigrationBackup() {
    this.logMessage('\n📦 Step 1: Creating migration backup...');
    
    try {
      // Backup database
      fs.copyFileSync(this.sqliteDbPath, this.migrationBackupPath);
      this.logMessage(`✅ Database backup created: ${this.migrationBackupPath}`);
      
      // Backup critical application files
      const filesToBackup = [
        'app.js',
        'package.json',
        '.env',
        'db/database.js',
        'models/',
        'middleware/'
      ];
      
      const backupDir = `./migration_backup_${Date.now()}`;
      fs.mkdirSync(backupDir, { recursive: true });
      
      filesToBackup.forEach(file => {
        if (fs.existsSync(file)) {
          const destPath = path.join(backupDir, file);
          const destDir = path.dirname(destPath);
          
          if (!fs.existsSync(destDir)) {
            fs.mkdirSync(destDir, { recursive: true });
          }
          
          if (fs.statSync(file).isDirectory()) {
            this.copyDirectory(file, destPath);
          } else {
            fs.copyFileSync(file, destPath);
          }
          this.logMessage(`✅ Backed up: ${file}`);
        }
      });
      
      this.logMessage(`✅ Application backup created: ${backupDir}`);
      
    } catch (error) {
      throw new Error(`Backup creation failed: ${error.message}`);
    }
  }

  copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    
    const files = fs.readdirSync(src);
    files.forEach(file => {
      const srcPath = path.join(src, file);
      const destPath = path.join(dest, file);
      
      if (fs.statSync(srcPath).isDirectory()) {
        this.copyDirectory(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    });
  }

  async analyzeCurrentDatabase() {
    this.logMessage('\n🔍 Step 2: Analyzing current database...');
    
    return new Promise((resolve, reject) => {
      const db = new sqlite3.Database(this.sqliteDbPath, sqlite3.OPEN_READONLY);
      
      // Get all tables
      db.all("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'", (err, tables) => {
        if (err) {
          reject(err);
          return;
        }
        
        this.logMessage(`📋 Found ${tables.length} tables to migrate:`);
        
        let analyzed = 0;
        const tableInfo = {};
        
        tables.forEach(table => {
          const tableName = table.name;
          
          // Get table schema
          db.all(`PRAGMA table_info(${tableName})`, (err, columns) => {
            if (err) {
              this.logMessage(`⚠️  Error analyzing table ${tableName}: ${err.message}`);
            } else {
              // Get row count
              db.get(`SELECT COUNT(*) as count FROM ${tableName}`, (err, countResult) => {
                if (!err) {
                  tableInfo[tableName] = {
                    columns: columns,
                    rowCount: countResult.count
                  };
                  this.logMessage(`   ✅ ${tableName}: ${columns.length} columns, ${countResult.count} rows`);
                }
                
                analyzed++;
                if (analyzed === tables.length) {
                  this.tableInfo = tableInfo;
                  db.close();
                  resolve();
                }
              });
            }
          });
        });
      });
    });
  }

  async generateSupabaseSchema() {
    this.logMessage('\n🏗️  Step 3: Generating Supabase schema...');
    
    const schemaSQL = [];
    schemaSQL.push('-- StreamOnPod Supabase Schema Migration');
    schemaSQL.push('-- Generated automatically from SQLite database');
    schemaSQL.push('-- Date: ' + new Date().toISOString());
    schemaSQL.push('');
    
    // Enable necessary extensions
    schemaSQL.push('-- Enable necessary PostgreSQL extensions');
    schemaSQL.push('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
    schemaSQL.push('CREATE EXTENSION IF NOT EXISTS "pgcrypto";');
    schemaSQL.push('');
    
    // Generate table schemas
    Object.entries(this.tableInfo).forEach(([tableName, info]) => {
      schemaSQL.push(`-- Table: ${tableName}`);
      schemaSQL.push(`CREATE TABLE IF NOT EXISTS public.${tableName} (`);
      
      const columnDefs = info.columns.map(col => {
        let pgType = this.sqliteToPostgresType(col.type);
        let constraints = [];
        
        if (col.pk) constraints.push('PRIMARY KEY');
        if (col.notnull && !col.pk) constraints.push('NOT NULL');
        if (col.dflt_value !== null) {
          if (col.dflt_value === 'CURRENT_TIMESTAMP') {
            constraints.push('DEFAULT CURRENT_TIMESTAMP');
          } else {
            constraints.push(`DEFAULT ${col.dflt_value}`);
          }
        }
        
        return `    ${col.name} ${pgType}${constraints.length ? ' ' + constraints.join(' ') : ''}`;
      });
      
      schemaSQL.push(columnDefs.join(',\n'));
      schemaSQL.push(');');
      schemaSQL.push('');
      
      // Add RLS policy
      schemaSQL.push(`-- Enable Row Level Security for ${tableName}`);
      schemaSQL.push(`ALTER TABLE public.${tableName} ENABLE ROW LEVEL SECURITY;`);
      schemaSQL.push('');
    });
    
    // Add indexes
    schemaSQL.push('-- Indexes for performance');
    schemaSQL.push('CREATE INDEX IF NOT EXISTS idx_users_username ON public.users(username);');
    schemaSQL.push('CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);');
    schemaSQL.push('CREATE INDEX IF NOT EXISTS idx_streams_user_id ON public.streams(user_id);');
    schemaSQL.push('CREATE INDEX IF NOT EXISTS idx_streams_status ON public.streams(status);');
    schemaSQL.push('CREATE INDEX IF NOT EXISTS idx_videos_user_id ON public.videos(user_id);');
    schemaSQL.push('CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);');
    schemaSQL.push('CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON public.transactions(user_id);');
    schemaSQL.push('');
    
    // Add basic RLS policies
    schemaSQL.push('-- Basic Row Level Security Policies');
    schemaSQL.push('-- Users can only see their own data');
    schemaSQL.push(`CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid()::text = id);`);
    schemaSQL.push(`CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid()::text = id);`);
    schemaSQL.push('');
    schemaSQL.push(`CREATE POLICY "Users can view own streams" ON public.streams FOR ALL USING (auth.uid()::text = user_id);`);
    schemaSQL.push(`CREATE POLICY "Users can view own videos" ON public.videos FOR ALL USING (auth.uid()::text = user_id);`);
    schemaSQL.push(`CREATE POLICY "Users can view own subscriptions" ON public.user_subscriptions FOR SELECT USING (auth.uid()::text = user_id);`);
    schemaSQL.push(`CREATE POLICY "Users can view own transactions" ON public.transactions FOR SELECT USING (auth.uid()::text = user_id);`);
    
    const schemaContent = schemaSQL.join('\n');
    fs.writeFileSync(this.schemaOutputPath, schemaContent);
    
    this.logMessage(`✅ Supabase schema generated: ${this.schemaOutputPath}`);
  }

  sqliteToPostgresType(sqliteType) {
    const typeMap = {
      'TEXT': 'TEXT',
      'INTEGER': 'INTEGER',
      'REAL': 'REAL',
      'BOOLEAN': 'BOOLEAN',
      'TIMESTAMP': 'TIMESTAMP WITH TIME ZONE',
      'BLOB': 'BYTEA'
    };
    
    const upperType = sqliteType.toUpperCase();
    return typeMap[upperType] || 'TEXT';
  }

  async exportDataForMigration() {
    this.logMessage('\n📤 Step 4: Exporting data for migration...');
    
    return new Promise((resolve, reject) => {
      const db = new sqlite3.Database(this.sqliteDbPath, sqlite3.OPEN_READONLY);
      const dataSQL = [];
      
      dataSQL.push('-- StreamOnPod Data Migration');
      dataSQL.push('-- Generated automatically from SQLite database');
      dataSQL.push('-- Date: ' + new Date().toISOString());
      dataSQL.push('');
      
      // Export data in dependency order
      const exportOrder = [
        'users', 'subscription_plans', 'user_subscriptions',
        'videos', 'streams', 'stream_history', 'transactions',
        'notifications', 'referrals', 'referral_earnings',
        'withdrawal_requests', 'role_permissions'
      ];
      
      let exported = 0;
      
      exportOrder.forEach(tableName => {
        if (this.tableInfo[tableName]) {
          db.all(`SELECT * FROM ${tableName}`, (err, rows) => {
            if (err) {
              this.logMessage(`⚠️  Error exporting ${tableName}: ${err.message}`);
            } else if (rows.length > 0) {
              dataSQL.push(`-- Data for table: ${tableName}`);
              
              const columns = Object.keys(rows[0]);
              const columnList = columns.join(', ');
              
              rows.forEach(row => {
                const values = columns.map(col => {
                  const value = row[col];
                  if (value === null) return 'NULL';
                  if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
                  if (typeof value === 'boolean') return value ? 'TRUE' : 'FALSE';
                  return value;
                });
                
                dataSQL.push(`INSERT INTO public.${tableName} (${columnList}) VALUES (${values.join(', ')});`);
              });
              
              dataSQL.push('');
              this.logMessage(`✅ Exported ${rows.length} records from ${tableName}`);
            }
            
            exported++;
            if (exported === exportOrder.length) {
              const dataContent = dataSQL.join('\n');
              fs.writeFileSync(this.dataOutputPath, dataContent);
              this.logMessage(`✅ Data export completed: ${this.dataOutputPath}`);
              db.close();
              resolve();
            }
          });
        } else {
          exported++;
          if (exported === exportOrder.length) {
            const dataContent = dataSQL.join('\n');
            fs.writeFileSync(this.dataOutputPath, dataContent);
            this.logMessage(`✅ Data export completed: ${this.dataOutputPath}`);
            db.close();
            resolve();
          }
        }
      });
    });
  }

  async generateMigrationInstructions() {
    this.logMessage('\n📋 Step 5: Generating migration instructions...');
    
    const instructions = `# StreamOnPod Supabase Migration Instructions

## 🎯 Migration Overview
This guide will help you migrate StreamOnPod from SQLite to Supabase PostgreSQL.

**Estimated Time**: 4-6 hours
**Complexity**: Medium
**Risk Level**: Medium (with proper backups)

## 📋 Prerequisites
- [ ] Node.js and npm installed
- [ ] Supabase account created
- [ ] Git repository backed up
- [ ] Database backup verified: ${this.migrationBackupPath}

## 🚀 Phase 1: Supabase Project Setup (30 minutes)

### 1.1 Create Supabase Project
1. Go to https://supabase.com/dashboard
2. Click "New Project"
3. Choose organization and enter project details:
   - Name: StreamOnPod
   - Database Password: [Generate strong password]
   - Region: [Choose closest to your users]
4. Wait for project creation (2-3 minutes)

### 1.2 Get Project Credentials
1. Go to Settings > API
2. Copy the following values:
   - Project URL
   - Project API Key (anon/public)
   - Project API Key (service_role/secret)

### 1.3 Install Supabase Dependencies
\`\`\`bash
npm install @supabase/supabase-js
npm install --save-dev @supabase/cli
\`\`\`

## 🏗️ Phase 2: Schema Migration (45 minutes)

### 2.1 Apply Database Schema
1. Go to Supabase Dashboard > SQL Editor
2. Copy and paste the contents of: ${this.schemaOutputPath}
3. Click "Run" to execute the schema
4. Verify all tables are created in Table Editor

### 2.2 Configure Row Level Security
The schema includes basic RLS policies. Review and adjust as needed for your security requirements.

## 📤 Phase 3: Data Migration (30 minutes)

### 3.1 Import Data
1. In Supabase Dashboard > SQL Editor
2. Copy and paste the contents of: ${this.dataOutputPath}
3. Click "Run" to import all data
4. Verify data in Table Editor

### 3.2 Verify Data Integrity
Check record counts match your SQLite database:
${Object.entries(this.tableInfo).map(([table, info]) => `- ${table}: ${info.rowCount} records`).join('\n')}

## 🔧 Phase 4: Code Refactoring (2-3 hours)

### 4.1 Environment Configuration
Add to your .env file:
\`\`\`
SUPABASE_URL=your-project-url
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key
\`\`\`

### 4.2 Database Connection
Replace SQLite connection with Supabase client.
See: supabase-database-adapter.js (will be generated)

### 4.3 Query Migration
Convert SQLite queries to Supabase syntax.
See: supabase-query-examples.js (will be generated)

## 🧪 Phase 5: Testing (1 hour)

### 5.1 Unit Testing
- [ ] Test database connections
- [ ] Test CRUD operations
- [ ] Test authentication
- [ ] Test subscription management

### 5.2 Integration Testing
- [ ] Test user registration/login
- [ ] Test stream creation/management
- [ ] Test video upload/playback
- [ ] Test payment processing

## 🔄 Phase 6: Rollback Plan

If issues arise:
1. Stop the application
2. Restore SQLite database: ${this.migrationBackupPath}
3. Revert code changes from backup
4. Restart application

## 📞 Support Resources

- Migration log: ${this.migrationLogPath}
- Schema file: ${this.schemaOutputPath}
- Data file: ${this.dataOutputPath}
- Supabase Documentation: https://supabase.com/docs

## ✅ Migration Checklist

- [ ] Phase 1: Supabase project created
- [ ] Phase 2: Schema migrated successfully
- [ ] Phase 3: Data imported and verified
- [ ] Phase 4: Code refactored and tested
- [ ] Phase 5: All tests passing
- [ ] Phase 6: Production deployment

---

**Next Steps**: 
1. Follow this guide step by step
2. Run the code generation tools
3. Test thoroughly before production deployment
`;

    fs.writeFileSync('./SUPABASE_MIGRATION_INSTRUCTIONS.md', instructions);
    this.logMessage('✅ Migration instructions generated: SUPABASE_MIGRATION_INSTRUCTIONS.md');
  }
}

// Run migration preparation
if (require.main === module) {
  const migrator = new SupabaseMigrationExecutor();
  migrator.executeMigration().catch(console.error);
}

module.exports = SupabaseMigrationExecutor;
