-- StreamOnPod Supabase Schema Migration
-- Generated automatically from SQLite database
-- Date: 2025-08-05T23:26:24.947Z

-- Enable necessary PostgreSQL extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Table: referrals
CREATE TABLE IF NOT EXISTS public.referrals (
    id TEXT PRIMARY KEY,
    referrer_id TEXT NOT NULL,
    referee_id TEXT,
    referral_code TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    commission_amount INTEGER DEFAULT 0,
    commission_paid BOOLEAN DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Enable Row Level Security for referrals
ALTER TABLE public.referrals ENABLE ROW LEVEL SECURITY;

-- Table: role_permissions
CREATE TABLE IF NOT EXISTS public.role_permissions (
    id TEXT PRIMARY KEY,
    role TEXT NOT NULL,
    permission TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable Row Level Security for role_permissions
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;

-- Table: referral_earnings
CREATE TABLE IF NOT EXISTS public.referral_earnings (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    referral_id TEXT NOT NULL,
    transaction_id TEXT,
    amount INTEGER NOT NULL,
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable Row Level Security for referral_earnings
ALTER TABLE public.referral_earnings ENABLE ROW LEVEL SECURITY;

-- Table: withdrawal_requests
CREATE TABLE IF NOT EXISTS public.withdrawal_requests (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    amount INTEGER NOT NULL,
    bank_name TEXT NOT NULL,
    account_number TEXT NOT NULL,
    account_name TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    admin_notes TEXT,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE,
    processed_by TEXT
);

-- Enable Row Level Security for withdrawal_requests
ALTER TABLE public.withdrawal_requests ENABLE ROW LEVEL SECURITY;

-- Table: streams
CREATE TABLE IF NOT EXISTS public.streams (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    video_id TEXT,
    rtmp_url TEXT NOT NULL,
    stream_key TEXT NOT NULL,
    platform TEXT,
    platform_icon TEXT,
    bitrate INTEGER DEFAULT 2500,
    resolution TEXT,
    fps INTEGER DEFAULT 30,
    orientation TEXT DEFAULT 'horizontal',
    loop_video BOOLEAN DEFAULT 1,
    schedule_time TIMESTAMP WITH TIME ZONE,
    schedule_timezone TEXT DEFAULT 'UTC',
    duration INTEGER,
    status TEXT DEFAULT 'offline',
    status_updated_at TIMESTAMP WITH TIME ZONE,
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    use_advanced_settings BOOLEAN DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    user_id TEXT
);

-- Enable Row Level Security for streams
ALTER TABLE public.streams ENABLE ROW LEVEL SECURITY;

-- Table: notifications
CREATE TABLE IF NOT EXISTS public.notifications (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT DEFAULT 'info',
    category TEXT DEFAULT 'system',
    priority TEXT DEFAULT 'normal',
    target_user_id TEXT,
    metadata TEXT,
    is_read BOOLEAN DEFAULT 0,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable Row Level Security for notifications
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Table: subscription_plans
CREATE TABLE IF NOT EXISTS public.subscription_plans (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    price REAL NOT NULL,
    currency TEXT DEFAULT 'USD',
    billing_period TEXT DEFAULT 'monthly',
    max_streaming_slots INTEGER DEFAULT 1,
    max_storage_gb INTEGER DEFAULT 5,
    features TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable Row Level Security for subscription_plans
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;

-- Table: users
CREATE TABLE IF NOT EXISTS public.users (
    id TEXT PRIMARY KEY,
    username TEXT NOT NULL,
    email TEXT,
    password TEXT NOT NULL,
    avatar_path TEXT,
    gdrive_api_key TEXT,
    role TEXT DEFAULT 'user',
    plan_type TEXT DEFAULT 'Preview',
    max_streaming_slots INTEGER DEFAULT 0,
    max_storage_gb INTEGER DEFAULT 2,
    used_storage_gb REAL DEFAULT 0,
    subscription_start_date TIMESTAMP WITH TIME ZONE,
    subscription_end_date TIMESTAMP WITH TIME ZONE,
    trial_start_date TIMESTAMP WITH TIME ZONE,
    trial_end_date TIMESTAMP WITH TIME ZONE,
    trial_slots INTEGER DEFAULT 0,
    trial_storage_gb REAL DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    referral_balance INTEGER DEFAULT 0,
    referred_by TEXT,
    first_commission_paid BOOLEAN DEFAULT 0,
    referral_code TEXT
);

-- Enable Row Level Security for users
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Table: transactions
CREATE TABLE IF NOT EXISTS public.transactions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    plan_id TEXT NOT NULL,
    order_id TEXT NOT NULL,
    amount_idr INTEGER NOT NULL,
    payment_method TEXT DEFAULT 'midtrans',
    status TEXT DEFAULT 'pending',
    midtrans_token TEXT,
    midtrans_redirect_url TEXT,
    midtrans_transaction_id TEXT,
    midtrans_payment_type TEXT,
    midtrans_transaction_time TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable Row Level Security for transactions
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;

-- Table: videos
CREATE TABLE IF NOT EXISTS public.videos (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    filepath TEXT NOT NULL,
    thumbnail_path TEXT,
    file_size INTEGER,
    duration REAL,
    format TEXT,
    resolution TEXT,
    bitrate INTEGER,
    fps TEXT,
    codec TEXT,
    audio_codec TEXT,
    user_id TEXT,
    upload_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    processing_status TEXT DEFAULT 'pending',
    streaming_ready_path TEXT,
    original_filepath TEXT
);

-- Enable Row Level Security for videos
ALTER TABLE public.videos ENABLE ROW LEVEL SECURITY;

-- Table: stream_history
CREATE TABLE IF NOT EXISTS public.stream_history (
    id TEXT PRIMARY KEY,
    stream_id TEXT,
    title TEXT NOT NULL,
    platform TEXT,
    platform_icon TEXT,
    video_id TEXT,
    video_title TEXT,
    resolution TEXT,
    bitrate INTEGER,
    fps INTEGER,
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    duration INTEGER,
    use_advanced_settings BOOLEAN DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    user_id TEXT
);

-- Enable Row Level Security for stream_history
ALTER TABLE public.stream_history ENABLE ROW LEVEL SECURITY;

-- Table: referral_clicks
CREATE TABLE IF NOT EXISTS public.referral_clicks (
    id TEXT PRIMARY KEY,
    referral_code TEXT NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    clicked_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable Row Level Security for referral_clicks
ALTER TABLE public.referral_clicks ENABLE ROW LEVEL SECURITY;

-- Table: user_subscriptions
CREATE TABLE IF NOT EXISTS public.user_subscriptions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    plan_id TEXT NOT NULL,
    status TEXT DEFAULT 'active',
    start_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP WITH TIME ZONE,
    payment_method TEXT,
    payment_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enable Row Level Security for user_subscriptions
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_username ON public.users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_streams_user_id ON public.streams(user_id);
CREATE INDEX IF NOT EXISTS idx_streams_status ON public.streams(status);
CREATE INDEX IF NOT EXISTS idx_videos_user_id ON public.videos(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON public.transactions(user_id);

-- Basic Row Level Security Policies
-- Users can only see their own data
CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid()::text = id);
CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid()::text = id);

CREATE POLICY "Users can view own streams" ON public.streams FOR ALL USING (auth.uid()::text = user_id);
CREATE POLICY "Users can view own videos" ON public.videos FOR ALL USING (auth.uid()::text = user_id);
CREATE POLICY "Users can view own subscriptions" ON public.user_subscriptions FOR SELECT USING (auth.uid()::text = user_id);
CREATE POLICY "Users can view own transactions" ON public.transactions FOR SELECT USING (auth.uid()::text = user_id);